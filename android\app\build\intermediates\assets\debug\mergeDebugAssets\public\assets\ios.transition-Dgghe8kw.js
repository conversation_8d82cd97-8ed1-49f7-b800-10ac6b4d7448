import{n as a,o as Z}from"./index-BHHSo8Ee.js";/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const j=540,z=o=>document.querySelector(`${o}.ion-cloned-element`),I=o=>o.shadowRoot||o,P=o=>{const i=o.tagName==="ION-TABS"?o:o.querySelector("ion-tabs"),l="ion-content ion-header:not(.header-collapse-condense-inactive) ion-title.title-large";if(i!=null){const e=i.querySelector("ion-tab:not(.tab-hidden), .ion-page:not(.ion-page-hidden)");return e!=null?e.querySelector(l):null}return o.querySelector(l)},D=(o,i)=>{const l=o.tagName==="ION-TABS"?o:o.querySelector("ion-tabs");let e=[];if(l!=null){const t=l.querySelector("ion-tab:not(.tab-hidden), .ion-page:not(.ion-page-hidden)");t!=null&&(e=t.querySelectorAll("ion-buttons"))}else e=o.querySelectorAll("ion-buttons");for(const t of e){const A=t.closest("ion-header"),E=A&&!A.classList.contains("header-collapse-condense-inactive"),h=t.querySelector("ion-back-button"),r=t.classList.contains("buttons-collapse"),g=t.slot==="start"||t.slot==="";if(h!==null&&g&&(r&&E&&i||!r))return h}return null},J=(o,i,l,e,t)=>{const A=D(e,l),E=P(t),h=P(e),r=D(t,l),g=A!==null&&E!==null&&!l,u=h!==null&&r!==null&&l;if(g){const S=E.getBoundingClientRect(),f=A.getBoundingClientRect(),c=I(A).querySelector(".button-text"),C=c==null?void 0:c.getBoundingClientRect(),y=I(E).querySelector(".toolbar-title").getBoundingClientRect();G(o,i,l,E,S,y,f,c,C),k(o,i,l,A,f,c,C,E,y)}else if(u){const S=h.getBoundingClientRect(),f=r.getBoundingClientRect(),c=I(r).querySelector(".button-text"),C=c==null?void 0:c.getBoundingClientRect(),y=I(h).querySelector(".toolbar-title").getBoundingClientRect();G(o,i,l,h,S,y,f,c,C),k(o,i,l,r,f,c,C,h,y)}return{forward:g,backward:u}},k=(o,i,l,e,t,A,E,h,r)=>{var g,u;const S=i?`calc(100% - ${t.right+4}px)`:`${t.left-4}px`,f=i?"right":"left",c=i?"left":"right",C=i?"right":"left";let L=1,y=1,T=`scale(${y})`;const b="scale(1)";if(A&&E){const Y=((g=A.textContent)===null||g===void 0?void 0:g.trim())===((u=h.textContent)===null||u===void 0?void 0:u.trim());L=r.width/E.width,y=(r.height-U)/E.height,T=Y?`scale(${L}, ${y})`:`scale(${y})`}const x=I(e).querySelector("ion-icon").getBoundingClientRect(),W=i?`${x.width/2-(x.right-t.right)}px`:`${t.left-x.width/2}px`,n=i?`-${window.innerWidth-t.right}px`:`${t.left}px`,p=`${r.top}px`,$=`${t.top}px`,v=[{offset:0,transform:`translate3d(${W}, ${p}, 0)`},{offset:1,transform:`translate3d(${n}, ${$}, 0)`}],s=[{offset:0,transform:`translate3d(${n}, ${$}, 0)`},{offset:1,transform:`translate3d(${W}, ${p}, 0)`}],d=l?s:v,q=l?[{offset:0,opacity:1,transform:b},{offset:1,opacity:0,transform:T}]:[{offset:0,opacity:0,transform:T},{offset:1,opacity:1,transform:b}],w=l?[{offset:0,opacity:1,transform:"scale(1)"},{offset:.2,opacity:0,transform:"scale(0.6)"},{offset:1,opacity:0,transform:"scale(0.6)"}]:[{offset:0,opacity:0,transform:"scale(0.6)"},{offset:.6,opacity:0,transform:"scale(0.6)"},{offset:1,opacity:1,transform:"scale(1)"}],N=a(),F=a(),B=a(),m=z("ion-back-button"),M=I(m).querySelector(".button-text"),H=I(m).querySelector("ion-icon");m.text=e.text,m.mode=e.mode,m.icon=e.icon,m.color=e.color,m.disabled=e.disabled,m.style.setProperty("display","block"),m.style.setProperty("position","fixed"),F.addElement(H),N.addElement(M),B.addElement(m),B.beforeStyles({position:"absolute",top:"0px",[C]:"0px"}).beforeAddWrite(()=>{e.style.setProperty("display","none"),m.style.setProperty(f,S)}).afterAddWrite(()=>{e.style.setProperty("display",""),m.style.setProperty("display","none"),m.style.removeProperty(f)}).keyframes(d),N.beforeStyles({"transform-origin":`${f} top`}).keyframes(q),F.beforeStyles({"transform-origin":`${c} center`}).keyframes(w),o.addAnimation([N,F,B])},G=(o,i,l,e,t,A,E,h,r)=>{var g,u;const S=i?"right":"left",f=i?`calc(100% - ${t.right}px)`:`${t.left}px`,c="0px",C=`${t.top}px`,L=8;let y=i?`-${window.innerWidth-E.right-L}px`:`${E.x+L}px`,T=.5;const b="scale(1)";let K=`scale(${T})`;if(h&&r){y=i?`-${window.innerWidth-r.right-L}px`:`${r.x-L}px`;const X=((g=h.textContent)===null||g===void 0?void 0:g.trim())===((u=e.textContent)===null||u===void 0?void 0:u.trim()),_=r.width/A.width;T=r.height/(A.height-U),K=X?`scale(${_}, ${T})`:`scale(${T})`}const x=E.top+E.height/2,W=t.height*T/2,n=`${x-W}px`,p=[{offset:0,opacity:0,transform:`translate3d(${y}, ${n}, 0) ${K}`},{offset:.1,opacity:0},{offset:1,opacity:1,transform:`translate3d(${c}, ${C}, 0) ${b}`}],$=[{offset:0,opacity:.99,transform:`translate3d(${c}, ${C}, 0) ${b}`},{offset:.6,opacity:0},{offset:1,opacity:0,transform:`translate3d(${y}, ${n}, 0) ${K}`}],v=l?p:$,s=z("ion-title"),d=a();s.innerText=e.innerText,s.size=e.size,s.color=e.color,d.addElement(s),d.beforeStyles({"transform-origin":`${S} top`,height:`${t.height}px`,display:"",position:"relative",[S]:f}).beforeAddWrite(()=>{e.style.setProperty("opacity","0")}).afterAddWrite(()=>{e.style.setProperty("opacity",""),s.style.setProperty("display","none")}).keyframes(v),o.addAnimation(d)},V=(o,i)=>{var l;try{const e="cubic-bezier(0.32,0.72,0,1)",t="opacity",A="transform",r=o.ownerDocument.dir==="rtl",g=r?"-99.5%":"99.5%",u=r?"33%":"-33%",S=i.enteringEl,f=i.leavingEl,c=i.direction==="back",C=S.querySelector(":scope > ion-content"),L=S.querySelectorAll(":scope > ion-header > *:not(ion-toolbar), :scope > ion-footer > *"),y=S.querySelectorAll(":scope > ion-header > ion-toolbar"),T=a(),b=a();if(T.addElement(S).duration(((l=i.duration)!==null&&l!==void 0?l:0)||j).easing(i.easing||e).fill("both").beforeRemoveClass("ion-page-invisible"),f&&o!==null&&o!==void 0){const n=a();n.addElement(o),T.addAnimation(n)}if(!C&&y.length===0&&L.length===0?b.addElement(S.querySelector(":scope > .ion-page, :scope > ion-nav, :scope > ion-tabs")):(b.addElement(C),b.addElement(L)),T.addAnimation(b),c?b.beforeClearStyles([t]).fromTo("transform",`translateX(${u})`,"translateX(0%)").fromTo(t,.8,1):b.beforeClearStyles([t]).fromTo("transform",`translateX(${g})`,"translateX(0%)"),C){const n=I(C).querySelector(".transition-effect");if(n){const p=n.querySelector(".transition-cover"),$=n.querySelector(".transition-shadow"),v=a(),s=a(),d=a();v.addElement(n).beforeStyles({opacity:"1",display:"block"}).afterStyles({opacity:"",display:""}),s.addElement(p).beforeClearStyles([t]).fromTo(t,0,.1),d.addElement($).beforeClearStyles([t]).fromTo(t,.03,.7),v.addAnimation([s,d]),b.addAnimation([v])}}const K=S.querySelector("ion-header.header-collapse-condense"),{forward:x,backward:W}=J(T,r,c,S,f);if(y.forEach(n=>{const p=a();p.addElement(n),T.addAnimation(p);const $=a();$.addElement(n.querySelector("ion-title"));const v=a(),s=Array.from(n.querySelectorAll("ion-buttons,[menuToggle]")),d=n.closest("ion-header"),X=d==null?void 0:d.classList.contains("header-collapse-condense-inactive");let _;c?_=s.filter(N=>{const F=N.classList.contains("buttons-collapse");return F&&!X||!F}):_=s.filter(N=>!N.classList.contains("buttons-collapse")),v.addElement(_);const q=a();q.addElement(n.querySelectorAll(":scope > *:not(ion-title):not(ion-buttons):not([menuToggle])"));const R=a();R.addElement(I(n).querySelector(".toolbar-background"));const O=a(),w=n.querySelector("ion-back-button");if(w&&O.addElement(w),p.addAnimation([$,v,q,R,O]),v.fromTo(t,.01,1),q.fromTo(t,.01,1),c)X||$.fromTo("transform",`translateX(${u})`,"translateX(0%)").fromTo(t,.01,1),q.fromTo("transform",`translateX(${u})`,"translateX(0%)"),O.fromTo(t,.01,1);else if(K||$.fromTo("transform",`translateX(${g})`,"translateX(0%)").fromTo(t,.01,1),q.fromTo("transform",`translateX(${g})`,"translateX(0%)"),R.beforeClearStyles([t,"transform"]),(d==null?void 0:d.translucent)?R.fromTo("transform",r?"translateX(-100%)":"translateX(100%)","translateX(0px)"):R.fromTo(t,.01,"var(--opacity)"),x||O.fromTo(t,.01,1),w&&!x){const F=a();F.addElement(I(w).querySelector(".button-text")).fromTo("transform",r?"translateX(-100px)":"translateX(100px)","translateX(0px)"),p.addAnimation(F)}}),f){const n=a(),p=f.querySelector(":scope > ion-content"),$=f.querySelectorAll(":scope > ion-header > ion-toolbar"),v=f.querySelectorAll(":scope > ion-header > *:not(ion-toolbar), :scope > ion-footer > *");if(!p&&$.length===0&&v.length===0?n.addElement(f.querySelector(":scope > .ion-page, :scope > ion-nav, :scope > ion-tabs")):(n.addElement(p),n.addElement(v)),T.addAnimation(n),c){n.beforeClearStyles([t]).fromTo("transform","translateX(0%)",r?"translateX(-100%)":"translateX(100%)");const s=Z(f);T.afterAddWrite(()=>{T.getDirection()==="normal"&&s.style.setProperty("display","none")})}else n.fromTo("transform","translateX(0%)",`translateX(${u})`).fromTo(t,1,.8);if(p){const s=I(p).querySelector(".transition-effect");if(s){const d=s.querySelector(".transition-cover"),X=s.querySelector(".transition-shadow"),_=a(),q=a(),R=a();_.addElement(s).beforeStyles({opacity:"1",display:"block"}).afterStyles({opacity:"",display:""}),q.addElement(d).beforeClearStyles([t]).fromTo(t,.1,0),R.addElement(X).beforeClearStyles([t]).fromTo(t,.7,.03),_.addAnimation([q,R]),n.addAnimation([_])}}$.forEach(s=>{const d=a();d.addElement(s);const X=a();X.addElement(s.querySelector("ion-title"));const _=a(),q=s.querySelectorAll("ion-buttons,[menuToggle]"),R=s.closest("ion-header"),O=R==null?void 0:R.classList.contains("header-collapse-condense-inactive"),w=Array.from(q).filter(H=>{const Y=H.classList.contains("buttons-collapse");return Y&&!O||!Y});_.addElement(w);const N=a(),F=s.querySelectorAll(":scope > *:not(ion-title):not(ion-buttons):not([menuToggle])");F.length>0&&N.addElement(F);const B=a();B.addElement(I(s).querySelector(".toolbar-background"));const m=a(),M=s.querySelector("ion-back-button");if(M&&m.addElement(M),d.addAnimation([X,_,N,m,B]),T.addAnimation(d),m.fromTo(t,.99,0),_.fromTo(t,.99,0),N.fromTo(t,.99,0),c){if(O||X.fromTo("transform","translateX(0%)",r?"translateX(-100%)":"translateX(100%)").fromTo(t,.99,0),N.fromTo("transform","translateX(0%)",r?"translateX(-100%)":"translateX(100%)"),B.beforeClearStyles([t,"transform"]),(R==null?void 0:R.translucent)?B.fromTo("transform","translateX(0px)",r?"translateX(-100%)":"translateX(100%)"):B.fromTo(t,"var(--opacity)",0),M&&!W){const Y=a();Y.addElement(I(M).querySelector(".button-text")).fromTo("transform","translateX(0%)",`translateX(${(r?-124:124)+"px"})`),d.addAnimation(Y)}}else O||X.fromTo("transform","translateX(0%)",`translateX(${u})`).fromTo(t,.99,0).afterClearStyles([A,t]),N.fromTo("transform","translateX(0%)",`translateX(${u})`).afterClearStyles([A,t]),m.afterClearStyles([t]),X.afterClearStyles([t]),_.afterClearStyles([t])})}return T}catch(e){throw e}},U=10;export{V as iosTransitionAnimation,I as shadow};

import { mutate } from "swr";
import { useCallback } from "react";
import { useUser } from "../useUser";
import { useScore } from "./useScore";

// Hook para acciones rápidas de score
export const useScoreActions = () => {
  const {
    addPointsForStoryCreation,
    addPointsForStoryCompletion,
    addPointsForDailyLogin,
    isLoading,
    error,
  } = useScore();
  const { user } = useUser();
  const scoreKey = user?.id ? `/score/${user.id}` : null;

  const addStoryCreationPoints = async (storyTitle?: string) => {
    if (!user?.id) return false;
    const success = await addPointsForStoryCreation(user.id, storyTitle);

    if (success && scoreKey) mutate(scoreKey);

    return success;
  };

  const addStoryCompletionPoints = async (storyTitle?: string) => {
    if (!user?.id) return false;
    const success = await addPointsForStoryCompletion(user.id, storyTitle);

    if (success && scoreKey) mutate(scoreKey);

    return success;
  };

  const addDailyLoginPoints = async () => {
    if (!user?.id) return false;
    const success = await addPointsForDailyLogin(user.id);

    if (success && scoreKey) mutate(scoreKey);

    return success;
  };

  return {
    addStoryCreationPoints,
    addStoryCompletionPoints,
    addDailyLoginPoints,
    isLoading,
    error,
  };
};

import useSWR, { mutate } from "swr";
import { useCallback } from "react";
import { useScore } from "./useScore";
import {
  UserRankingDto,
} from "../../common/models/score.interface";

// Hook para ranking
export const useScoreRanking = () => {
  const { getRanking } = useScore();

  const limit = 30;

  // Key de SWR: incluimos limit para cache independiente por límite
  const key = `/score/ranking?limit=${limit}`;

  const fetcher = async () => {
    const ranking = await getRanking({ limit });
    if (!ranking) throw new Error("No se pudo obtener el ranking");
    return ranking;
  };

  const { data, error, isLoading } = useSWR<UserRankingDto[]>(key, fetcher, {
    revalidateOnFocus: false,
    dedupingInterval: 1000 * 60 * 30, // 30 minutos
  });

  // Función para refrescar manualmente los datos
  const refetch = useCallback(async () => {
    await mutate(key);
  }, [key]);

  return {
    ranking: data ?? [],
    isLoading,
    error,
    refetch,
  };
};

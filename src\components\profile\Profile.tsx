import {
  IonContent,
  IonPage,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonIcon,
  IonButton,
  IonChip,
  IonBadge
} from '@ionic/react';
import {
  person,
  mail,
  logOut,
  star,
  calendar,
  shield,
  trophyOutline,
  ribbonOutline,
  medalOutline,
  barChartOutline
} from 'ionicons/icons';
import { useUser } from '../../hooks/useUser';
import { useAuth } from '../../hooks/useAuth';
import { useUserScore, useUserRankingPosition } from '../../hooks/score';
import './Profile.scss';

import Header from '../header/Header';

export default function Profile() {
  const { user } = useUser();
  const { logout } = useAuth();
  const { totalScore } = useUserScore();
  const { userPosition, rankingColor } = useUserRankingPosition();

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  // Calcular progreso al siguiente nivel
  const calculateProgress = (score: number) => {
    const pointsPerLevel = 50;
    const currentLevel = Math.floor(score / pointsPerLevel) + 1;
    const pointsInCurrentLevel = score % pointsPerLevel;
    const progressPercentage = (pointsInCurrentLevel / pointsPerLevel) * 100;
    const pointsToNextLevel = pointsPerLevel - pointsInCurrentLevel;

    return {
      currentLevel,
      progressPercentage: Math.round(progressPercentage),
      pointsToNextLevel
    };
  };

  const progress = calculateProgress(totalScore);

  // Helper function to format date
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'No disponible';
    try {
      return new Date(dateString).toLocaleDateString('es-ES', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return 'No disponible';
    }
  };

  // Helper function to get user initials
  const getUserInitials = (name?: string) => {
    if (!name) return 'U';
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  };

  // Helper function to get ranking tier info
  const getRankingTierInfo = () => {
    if (!userPosition) return null;

    if (userPosition <= 10) {
      return { tier: 'Platino', icon: trophyOutline, color: '#00CED1' };
    } else if (userPosition <= 20) {
      return { tier: 'Oro', icon: medalOutline, color: '#FFD700' };
    } else if (userPosition <= 30) {
      return { tier: 'Plata', icon: ribbonOutline, color: '#C0C0C0' };
    }
    return null;
  };

  const tierInfo = getRankingTierInfo();

  return (
    <IonPage>
      <Header />
      <IonContent>
        <div className="profile-container">
          {/* Profile Header */}
          <div
            className={`profile-header ${tierInfo ? 'ranking-tier' : ''}`}
            style={tierInfo ? {
              background: `linear-gradient(135deg, ${tierInfo.color}15, var(--ion-background-color))`,
              border: `2px solid ${tierInfo.color}40`
            } : {}}
          >
            <div className="profile-avatar">
              <div
                className="avatar-circle"
                style={tierInfo ? {
                  background: `linear-gradient(135deg, ${tierInfo.color}, ${tierInfo.color}CC)`,
                  boxShadow: `0 8px 24px ${tierInfo.color}40`
                } : {}}
              >
                {getUserInitials(user?.name)}
              </div>
              <div className="avatar-status">
                <IonIcon icon={shield} />
              </div>
            </div>
            <div className="profile-info">
              <h1 className="profile-name">{user?.name || 'Usuario Invitado'}</h1>
              <p className="profile-email">{user?.email || 'Email no disponible'}</p>

              {/* Leyenda del ranking */}
              {tierInfo && (
                <div className="ranking-badge">
                  <IonIcon
                    icon={tierInfo.icon}
                    style={{ color: tierInfo.color }}
                  />
                  <span style={{ color: tierInfo.color }}>
                    {tierInfo.tier} - Posición #{userPosition}
                  </span>
                </div>
              )}

              <div className="profile-stats">
                <IonChip className="stat-chip">
                  <IonIcon
                    icon={star}
                    style={tierInfo ? { color: tierInfo.color } : {}}
                  />
                  <span>{totalScore} Puntos</span>
                </IonChip>
                <IonChip className="stat-chip level-chip">
                  <IonIcon icon={trophyOutline} />
                  <span>Nivel {progress.currentLevel}</span>
                </IonChip>
                <IonBadge className="status-badge" color="success">Activo</IonBadge>
              </div>
            </div>
          </div>

          {/* User Information Card */}
          <IonCard className="info-card">
            <IonCardHeader>
              <IonCardTitle>
                <IonIcon icon={person} />
                Detalles de la Cuenta
              </IonCardTitle>
            </IonCardHeader>
            <IonCardContent>
              <div className="info-grid">
                <div className="info-item">
                  <div className="info-icon">
                    <IonIcon icon={person} />
                  </div>
                  <div className="info-content">
                    <span className="info-label">Nombre Completo</span>
                    <span className="info-value">{user?.name || 'No disponible'}</span>
                  </div>
                </div>

                <div className="info-item">
                  <div className="info-icon">
                    <IonIcon icon={mail} />
                  </div>
                  <div className="info-content">
                    <span className="info-label">Dirección de Email</span>
                    <span className="info-value">{user?.email || 'No disponible'}</span>
                  </div>
                </div>
              </div>
            </IonCardContent>
          </IonCard>

          {/* Points Statistics Card */}
          <IonCard className="points-card">
            <IonCardHeader>
              <IonCardTitle>
                <IonIcon icon={barChartOutline} />
                Estadísticas de Puntos
              </IonCardTitle>
            </IonCardHeader>
            <IonCardContent>
              <div className="points-grid">
                <div className="points-stat-item">
                  <div className="stat-icon">
                    <IonIcon icon={star} color="warning" />
                  </div>
                  <div className="stat-content">
                    <span className="stat-label">Puntos Totales</span>
                    <span className="stat-value">{totalScore}</span>
                  </div>
                </div>

                <div className="points-stat-item">
                  <div className="stat-icon">
                    <IonIcon icon={trophyOutline} color="primary" />
                  </div>
                  <div className="stat-content">
                    <span className="stat-label">Nivel Actual</span>
                    <span className="stat-value">Nivel {progress.currentLevel}</span>
                    <span className="stat-sublabel">
                      {progress.currentLevel === 1 ? 'Principiante' :
                       progress.currentLevel <= 3 ? 'Novato' :
                       progress.currentLevel <= 5 ? 'Intermedio' :
                       progress.currentLevel <= 8 ? 'Avanzado' : 'Experto'}
                    </span>
                  </div>
                </div>

                <div className="points-stat-item">
                  <div className="stat-icon">
                    <IonIcon icon={ribbonOutline} color="success" />
                  </div>
                  <div className="stat-content">
                    <span className="stat-label">Progreso al Siguiente Nivel</span>
                    <div className="progress-container">
                      <div className="progress-bar">
                        <div
                          className="progress-fill"
                          style={{ width: `${progress.progressPercentage}%` }}
                        ></div>
                      </div>
                      <span className="progress-text">{progress.progressPercentage}%</span>
                    </div>
                    <span className="stat-sublabel">
                      {progress.pointsToNextLevel} puntos para el siguiente nivel
                    </span>
                  </div>
                </div>

                <div className="points-stat-item">
                  <div className="stat-icon">
                    <IonIcon icon={medalOutline} color="tertiary" />
                  </div>
                  <div className="stat-content">
                    <span className="stat-label">Ranking General</span>
                    <span className="stat-value">Posición #1</span>
                    <span className="stat-sublabel">¡Eres el mejor!</span>
                  </div>
                </div>
              </div>

              <div className="points-actions">
                <IonButton
                  fill="outline"
                  size="small"
                  routerLink="/tabs/rankings"
                  className="points-action-btn"
                >
                  <IonIcon icon={trophyOutline} slot="start" />
                  Ver Rankings
                </IonButton>
              </div>
            </IonCardContent>
          </IonCard>

          {/* Actions Card */}
          <IonCard className="actions-card">
            <IonCardHeader>
              <IonCardTitle>
                <IonIcon icon={logOut} />
                Acciones de la Cuenta
              </IonCardTitle>
            </IonCardHeader>
            <IonCardContent>
              <IonButton
                className="action-button logout-btn"
                expand="block"
                fill="solid"
                color="danger"
                onClick={handleLogout}
              >
                <IonIcon icon={logOut} slot="start" />
                Cerrar Sesión
              </IonButton>
            </IonCardContent>
          </IonCard>
        </div>
      </IonContent>
    </IonPage>
  );
}

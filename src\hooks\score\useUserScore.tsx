import useSWR, { mutate } from "swr";
import { useUser } from "../useUser";
import { useScore } from "./useScore";
import { ScoreResponseDto } from "../../common/models/score.interface";

export const useUserScore = (userId?: string) => {
  const { getUserScore } = useScore();
  const { user } = useUser();

  const currentUserId = userId || user?.id;
  const shouldFetch = !!currentUserId;

  // SWR fetcher que nunca devuelve null
  const fetcher = async () => {
    if (!currentUserId) throw new Error("No userId");
    const score = await getUserScore(currentUserId);
    if (!score) throw new Error("No se pudo obtener el score");
    return score;
  };

  const { data, error, isLoading } = useSWR<ScoreResponseDto>(
    shouldFetch ? `/score/${currentUserId}` : null,
    fetcher
  );

  const refreshScore = () => {
    if (!currentUserId) return;
    mutate(`/score/${currentUserId}`);
  };

  return {
    userScore: data ?? null,
    isLoading,
    error,
    totalScore: data?.totalScore || 0,
    refreshScore, // llamar después de sumar puntos
  };
};

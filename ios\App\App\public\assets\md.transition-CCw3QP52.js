import{o as g,n as r}from"./index-AVUrVRd1.js";/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const v=(u,i)=>{var o,t,a;const l="40px",s=i.direction==="back",E=i.enteringEl,c=i.leavingEl,d=g(E),m=d.querySelector("ion-toolbar"),n=r();if(n.addElement(d).fill("both").beforeRemoveClass("ion-page-invisible"),s?n.duration(((o=i.duration)!==null&&o!==void 0?o:0)||200).easing("cubic-bezier(0.47,0,0.745,0.715)"):n.duration(((t=i.duration)!==null&&t!==void 0?t:0)||280).easing("cubic-bezier(0.36,0.66,0.04,1)").fromTo("transform",`translateY(${l})`,"translateY(0px)").fromTo("opacity",.01,1),m){const e=r();e.addElement(m),n.addAnimation(e)}if(c&&s){n.duration(((a=i.duration)!==null&&a!==void 0?a:0)||200).easing("cubic-bezier(0.47,0,0.745,0.715)");const e=r();e.addElement(g(c)).onFinish(b=>{b===1&&e.elements.length>0&&e.elements[0].style.setProperty("display","none")}).fromTo("transform","translateY(0px)",`translateY(${l})`).fromTo("opacity",1,0),n.addAnimation(e)}return n};export{v as mdTransitionAnimation};

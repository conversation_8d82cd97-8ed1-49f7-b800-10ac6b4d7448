<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":capacitor-cordova-android-plugins" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\capacitor-cordova-android-plugins\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":capacitor-android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\node_modules\@capacitor\android\capacitor\build\intermediates\library_assets\debug\packageDebugAssets\out"><file name="native-bridge.js" path="C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\node_modules\@capacitor\android\capacitor\build\intermediates\library_assets\debug\packageDebugAssets\out\native-bridge.js"/></source></dataSet><dataSet config=":capacitor-status-bar" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\node_modules\@capacitor\status-bar\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":capacitor-splash-screen" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\node_modules\@capacitor\splash-screen\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":capacitor-keyboard" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\node_modules\@capacitor\keyboard\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":capacitor-haptics" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\node_modules\@capacitor\haptics\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":capacitor-app" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\node_modules\@capacitor\app\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":capacitor-community-text-to-speech" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\node_modules\@capacitor-community\text-to-speech\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":boengli-capacitor-fullscreen" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\node_modules\@boengli\capacitor-fullscreen\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\assets"><file name="capacitor.config.json" path="C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\assets\capacitor.config.json"/><file name="capacitor.plugins.json" path="C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\assets\capacitor.plugins.json"/><file name="public/assets/favicon-f424P6OJ.png" path="C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\assets\public\assets\favicon-f424P6OJ.png"/><file name="public/assets/focus-visible-supuXXMI.js" path="C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\assets\public\assets\focus-visible-supuXXMI.js"/><file name="public/assets/logo.svg" path="C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\assets\public\assets\logo.svg"/><file name="public/cordova.js" path="C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\assets\public\cordova.js"/><file name="public/cordova_plugins.js" path="C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\assets\public\cordova_plugins.js"/><file name="public/favicon.png" path="C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\assets\public\favicon.png"/><file name="public/index.html" path="C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\assets\public\index.html"/><file name="public/manifest.json" path="C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\assets\public\manifest.json"/><file name="public/assets/index-AVUrVRd1.js" path="C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\assets\public\assets\index-AVUrVRd1.js"/><file name="public/assets/index-zruIOjXr.css" path="C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\assets\public\assets\index-zruIOjXr.css"/><file name="public/assets/index7-BG8s2U4I.js" path="C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\assets\public\assets\index7-BG8s2U4I.js"/><file name="public/assets/input-shims-WhQqloge.js" path="C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\assets\public\assets\input-shims-WhQqloge.js"/><file name="public/assets/ios.transition-Bbq0euRB.js" path="C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\assets\public\assets\ios.transition-Bbq0euRB.js"/><file name="public/assets/keyboard-BwKRwW11.js" path="C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\assets\public\assets\keyboard-BwKRwW11.js"/><file name="public/assets/md.transition-CCw3QP52.js" path="C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\assets\public\assets\md.transition-CCw3QP52.js"/><file name="public/assets/status-tap-CH1VX6jD.js" path="C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\assets\public\assets\status-tap-CH1VX6jD.js"/><file name="public/assets/swipe-back-CasHjM8d.js" path="C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\assets\public\assets\swipe-back-CasHjM8d.js"/><file name="public/assets/web-BbZTaGRz.js" path="C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\assets\public\assets\web-BbZTaGRz.js"/><file name="public/assets/web-D3tuQfz9.js" path="C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\assets\public\assets\web-D3tuQfz9.js"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>
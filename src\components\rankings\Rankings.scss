/* Estilos para el componente Rankings */

.rankings-page-header {
  padding: 16px 16px 8px;
  text-align: center;
  background: var(--ion-background-color);
}

.rankings-title {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--ion-color-primary);
}

.user-stats-card {
  margin: 16px;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  background: var(--ion-card-background);
  color: var(--ion-color-dark);
}

.user-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-top: 8px;
}

.stat-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: var(--ion-color-light-tint);
  border-radius: 12px;
  border: 1px solid var(--ion-color-light);

  ion-icon {
    font-size: 1.3rem;
    color: var(--ion-color-primary);
    margin-top: 2px;
    flex-shrink: 0;
  }
}

.stat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-label {
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--ion-color-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.ion-list-styles {
  background: transparent
}

.stat-value {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--ion-text-color);
}

.stat-sublabel {
  font-size: 0.75rem;
  color: var(--ion-color-medium);
  font-weight: 500;
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 4px 0;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: var(--ion-color-light);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--ion-color-success), var(--ion-color-success-tint));
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--ion-color-success);
  min-width: 30px;
  text-align: right;
}

.rankings-container {
  padding: 4px 12px 12px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  gap: 16px;
}

.loading-container ion-spinner {
  --color: var(--primary-color);
  transform: scale(1.5);
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  gap: 16px;
  text-align: center;
}

.error-icon {
  font-size: 4rem;
  color: var(--ion-color-danger);
  margin-bottom: 8px;
}

.error-container h3 {
  margin: 0 0 8px 0;
  color: var(--ion-color-danger);
  font-size: 1.2rem;
  font-weight: 600;
}

.error-container p {
  margin: 0 0 16px 0;
  color: var(--ion-color-medium);
  font-size: 0.9rem;
  line-height: 1.4;
}

.ranking-item {
  --padding-start: 16px;
  --padding-end: 16px;
  --inner-padding-end: 0;
  --background: var(--ion-item-background);
  --color: var(--ion-color-dark);
  margin-bottom: 6px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  min-height: 56px;
}

.ranking-position {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 10px;
  min-width: 36px;
}

.position-icon {
  font-size: 1.6rem;
  margin-bottom: 1px;

  &.gold {
    color: #FFD700;
    filter: drop-shadow(0 2px 4px rgba(255, 215, 0, 0.3));
  }

  &.silver {
    color: #C0C0C0;
    filter: drop-shadow(0 2px 4px rgba(192, 192, 192, 0.3));
  }

  &.bronze {
    color: #CD7F32;
    filter: drop-shadow(0 2px 4px rgba(205, 127, 50, 0.3));
  }

  &.default {
    color: var(--ion-color-medium);
    font-weight: 700;
  }
}

.position-number {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--ion-color-medium);
}

.user-avatar {
  width: 40px;
  height: 40px;
  margin-right: 10px;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-color);
  color: white;
  font-weight: 600;
  font-size: 1rem;
  border-radius: 50%;
}

.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.user-name {
  margin: 0;
  font-size: 1rem;
  font-weight: 700;
  color: var(--ion-text-color);
}

.user-level {
  margin: 2px 0 0 0;
  font-size: 0.8rem;
  color: var(--ion-color-medium);
  font-weight: 500;
}

.user-points {
  display: flex;
  align-items: center;
}

.points-badge {
  --background: var(--primary-color);
  --color: white;
  font-weight: 700;
  font-size: 0.8rem;
  padding: 6px 12px;
  border-radius: 16px;
  min-width: 60px;
  text-align: center;
}

.empty-rankings {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 4rem;
  color: var(--ion-color-light);
  margin-bottom: 16px;
}

.empty-rankings h3 {
  margin: 0 0 8px 0;
  color: var(--ion-color-medium);
  font-size: 1.1rem;
}

.empty-rankings p {
  margin: 0;
  color: var(--ion-color-medium);
  font-size: 0.9rem;
}

.mock-data-indicator {
  text-align: center;
  padding: 16px;
  margin-top: 8px;
}

.mock-data-indicator p {
  margin: 0;
  font-size: 0.85rem;
  font-style: italic;
  opacity: 0.7;
}

/* Estilos específicos para marcos de ranking */

/* Marco de Platino (posiciones 1-10) - Turquesa fuerte */
.ranking-item.platinum-frame {
  --background: linear-gradient(135deg, rgba(0, 206, 209, 0.18), var(--ion-item-background));
  border: 3px solid transparent;
  background-image:
    linear-gradient(var(--ion-item-background), var(--ion-item-background)),
    linear-gradient(135deg, #00CED1, #20B2AA, #00FFFF, #008B8B);
  background-origin: border-box;
  background-clip: padding-box, border-box;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: linear-gradient(135deg, #00CED1, #20B2AA, #00FFFF, #008B8B);
    border-radius: 19px;
    z-index: -1;
    animation: platinum-glow 2.5s ease-in-out infinite alternate;
  }
}

@keyframes platinum-glow {
  0% {
    opacity: 0.8;
    filter: brightness(1);
  }
  100% {
    opacity: 1;
    filter: brightness(1.2);
  }
}

/* Marco de Oro (posiciones 11-20) */
.ranking-item.gold-frame {
  --background: linear-gradient(135deg, rgba(255, 215, 0, 0.12), var(--ion-item-background));
  border: 3px solid transparent;
  background-image:
    linear-gradient(var(--ion-item-background), var(--ion-item-background)),
    linear-gradient(135deg, #FFD700, #FFA500, #FFD700, #DAA520);
  background-origin: border-box;
  background-clip: padding-box, border-box;
  box-shadow:
    0 6px 20px rgba(255, 215, 0, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: linear-gradient(135deg, #FFD700, #FFA500, #FFD700, #DAA520);
    border-radius: 19px;
    z-index: -1;
    animation: gold-glow 2.5s ease-in-out infinite alternate;
  }
}

@keyframes gold-glow {
  0% { opacity: 0.6; }
  100% { opacity: 0.9; }
}

/* Marco de Plata (posiciones 21-30) */
.ranking-item.silver-frame {
  --background: linear-gradient(135deg, rgba(192, 192, 192, 0.1), var(--ion-item-background));
  border: 3px solid transparent;
  background-image:
    linear-gradient(var(--ion-item-background), var(--ion-item-background)),
    linear-gradient(135deg, #C0C0C0, #E6E6FA, #C0C0C0, #A9A9A9);
  background-origin: border-box;
  background-clip: padding-box, border-box;
  box-shadow:
    0 5px 15px rgba(192, 192, 192, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: linear-gradient(135deg, #C0C0C0, #E6E6FA, #C0C0C0, #A9A9A9);
    border-radius: 19px;
    z-index: -1;
    animation: silver-glow 2s ease-in-out infinite alternate;
  }
}

@keyframes silver-glow {
  0% { opacity: 0.5; }
  100% { opacity: 0.8; }
}

/* Estilos específicos para iconos según el marco */
.platinum-frame .position-icon {
  filter: drop-shadow(0 3px 8px rgba(0, 206, 209, 0.6)) drop-shadow(0 0 10px rgba(0, 206, 209, 0.3));
  font-size: 1.8rem;
  animation: platinum-icon-glow 2s ease-in-out infinite alternate;
}

@keyframes platinum-icon-glow {
  0% {
    filter: drop-shadow(0 3px 8px rgba(0, 206, 209, 0.6)) drop-shadow(0 0 10px rgba(0, 206, 209, 0.3));
  }
  100% {
    filter: drop-shadow(0 3px 12px rgba(0, 206, 209, 0.8)) drop-shadow(0 0 15px rgba(0, 206, 209, 0.5));
  }
}

.gold-frame .position-icon {
  filter: drop-shadow(0 2px 6px rgba(255, 215, 0, 0.4));
  font-size: 1.7rem;
}

.silver-frame .position-icon {
  filter: drop-shadow(0 2px 4px rgba(192, 192, 192, 0.4));
  font-size: 1.65rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .user-stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .stat-item {
    padding: 12px;
  }

  .stat-value {
    font-size: 1.1rem;
  }
  
  .user-name {
    font-size: 0.85rem;
  }

  .user-level {
    font-size: 0.7rem;
  }

  .points-badge {
    font-size: 0.75rem;
    padding: 4px 8px;
    min-width: 50px;
  }

  .ranking-item {
    min-height: 50px;
    margin-bottom: 4px;
  }

  .user-avatar {
    width: 36px;
    height: 36px;
  }

  .avatar-placeholder {
    font-size: 0.9rem;
  }
}

/* Estilos para el modal compacto */
.user-position-banner {
  background: var(--primary-color);
  color: white;
  padding: 16px;
  text-align: center;
  margin: 16px;
  border-radius: 12px;
}

.user-position-banner h4 {
  margin: 0 0 4px 0;
  font-size: 1rem;
  font-weight: 600;
}

.user-position-banner p {
  margin: 0;
  font-size: 0.85rem;
  opacity: 0.9;
}

.ranking-item-compact {
  --padding-start: 12px;
  --padding-end: 12px;
  margin-bottom: 4px;
  border-radius: 8px;
  background: white;
}

.ranking-position-compact {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-right: 12px;
  min-width: 60px;
}

.position-icon-compact {
  font-size: 1.2rem;
}

.user-info-compact {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.points-badge-compact {
  --background: var(--primary-color);
  --color: white;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
}

.modal-footer {
  padding: 16px;
}

.close-button {
  --border-color: var(--primary-color);
  --color: var(--primary-color);
}

/* Tema oscuro */
.dark-theme {
  .rankings-page-header {
    background: var(--ion-background-color);
    border-bottom-color: var(--ion-color-dark);
  }

  .rankings-title {
    color: var(--ion-color-primary);
  }

  .rankings-subtitle {
    color: var(--ion-color-medium);
  }

  .user-stats-card {
    background: var(--ion-card-background);
    color: var(--ion-text-color);
  }

  .stat-item {
    background: var(--ion-color-dark-tint);
    border-color: var(--ion-color-dark);
  }

  .progress-bar {
    background: var(--ion-color-dark);
  }

  .ranking-item {
    --background: var(--ion-item-background);
    --color: var(--ion-text-color);
  }

  .ranking-item-compact {
    --background: var(--ion-item-background);
    --color: var(--ion-text-color);
  }

  .user-name {
    color: var(--ion-text-color);
  }

  /* Marcos para tema oscuro */
  .ranking-item.platinum-frame {
    --background: linear-gradient(135deg, rgba(0, 206, 209, 0.25), var(--ion-item-background));
  }

  .ranking-item.gold-frame {
    --background: linear-gradient(135deg, rgba(255, 215, 0, 0.18), var(--ion-item-background));
    box-shadow:
      0 6px 20px rgba(255, 215, 0, 0.35),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .ranking-item.silver-frame {
    --background: linear-gradient(135deg, rgba(192, 192, 192, 0.15), var(--ion-item-background));
    box-shadow:
      0 5px 15px rgba(192, 192, 192, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }
}

/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const v="ion-focused",L="ion-focusable",m=["Tab","ArrowDown","Space","Escape"," ","Shift","Enter","ArrowLeft","ArrowR<PERSON>","<PERSON>Up","Home","End"],h=n=>{let i=[],r=!0;const e=n?n.shadowRoot:document,E=n||document.body,o=t=>{i.forEach(s=>s.classList.remove(v)),t.forEach(s=>s.classList.add(v)),i=t},c=()=>{r=!1,o([])},d=t=>{r=m.includes(t.key),r||o([])},u=t=>{if(r&&t.composedPath!==void 0){const s=t.composedPath().filter(f=>f.classList?f.classList.contains(L):!1);o(s)}},a=()=>{e.activeElement===E&&o([])};return e.addEventListener("keydown",d),e.addEventListener("focusin",u),e.addEventListener("focusout",a),e.addEventListener("touchstart",c,{passive:!0}),e.addEventListener("mousedown",c),{destroy:()=>{e.removeEventListener("keydown",d),e.removeEventListener("focusin",u),e.removeEventListener("focusout",a),e.removeEventListener("touchstart",c),e.removeEventListener("mousedown",c)},setFocus:o}};export{h as startFocusVisible};

/* ===== CARD GALLERY - ESTILOS MODERNOS Y ELEGANTES ===== */

/* Card principal con tema adaptativo */
.story-card {
  --background: var(--card-background);
  --color: var(--text-dark);
  background: var(--card-background);
  color: var(--text-dark);
  border-radius: 20px;
  border: 1px solid var(--border-color-light);
  box-shadow: 0 6px 20px var(--shadow-color-light);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  position: relative;
  cursor: pointer;
  margin: 0;

  /* Hover effect elegante */
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 32px var(--shadow-color);
    border-color: var(--primary-color);
  }

  /* Estado de eliminación */
  &.deleting {
    opacity: 0.6;
    transform: scale(0.98);
    cursor: not-allowed;

    &:hover {
      transform: scale(0.98);
      box-shadow: 0 6px 20px var(--shadow-color-light);
    }
  }
}

/* Header del card */
.header-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
  position: relative;
}

/* Avatar circular con inicial */
.card-avatar {
  flex-shrink: 0;

  .avatar-circle {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
    color: var(--text-on-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    font-weight: 700;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 12px rgba(74, 111, 165, 0.3);
    transition: all 0.3s ease;
  }
}

/* Título del card */
.card-title {
  flex: 1;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-dark);
  line-height: 1.4;
  margin: 0;
  padding: 0;
  cursor: pointer;
  transition: color 0.3s ease;
  letter-spacing: 0.3px;

  &:hover {
    color: var(--primary-color);
  }
}

/* Botón de opciones */
.button-delete {
  background: transparent;
  border: none;
  padding: 8px;
  border-radius: 50%;
  color: var(--text-medium);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;

  &:hover {
    background: var(--background-secondary);
    color: var(--text-dark);
    transform: scale(1.1);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;

    &:hover {
      transform: none;
      background: transparent;
    }
  }

  ion-icon {
    font-size: 1.2rem;
  }
}

/* Metadatos del card */
.card-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
  margin-bottom: 8px;
}

/* Chip de idioma */
.language-chip {
  --background: var(--background-secondary);
  --color: var(--text-medium);
  border: 1px solid var(--border-color-light);
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  height: 28px;

  ion-icon {
    font-size: 0.9rem;
    color: var(--primary-color);
    margin-right: 4px;
  }

  ion-label {
    font-weight: 500;
    letter-spacing: 0.2px;
  }
}

/* Badge de dificultad */
.difficulty-badge {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 4px 10px;
  border-radius: 12px;
  letter-spacing: 0.3px;
  text-transform: uppercase;
}

/* Contenido del card */
.card-content {
  padding: 16px 20px 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Detalles del card */
.card-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  color: var(--text-medium);

  ion-icon {
    font-size: 1rem;
    color: var(--primary-color);
    flex-shrink: 0;
  }

  span {
    font-weight: 500;
    letter-spacing: 0.2px;
  }
}

/* Acción del card */
.card-action {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  margin: 0 -4px -4px -4px;
  border-radius: 12px;
  background: var(--primary-color);
  color: var(--text-on-primary);
  font-size: 0.9rem;
  font-weight: 600;
  letter-spacing: 0.3px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(74, 111, 165, 0.2);

  span {
    text-transform: uppercase;
  }

  ion-icon {
    font-size: 1.1rem;
    transition: transform 0.3s ease;
  }

  /* Hover effect para el botón */
  &:hover {
    background: var(--primary-color-dark);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(74, 111, 165, 0.3);

    ion-icon {
      transform: translateX(4px);
    }
  }
}

/* Texto de eliminación */
.deleting-text {
  color: var(--text-medium);
  font-style: italic;
  font-weight: 500;
}

/* ===== ADAPTACIONES PARA TEMA OSCURO ===== */
body.dark-theme {
  .story-card {
    border-color: var(--border-color);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);

    &:hover {
      box-shadow: 0 12px 32px rgba(0, 0, 0, 0.4);
      border-color: var(--primary-color);
    }

    &.deleting:hover {
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    }
  }

  .card-avatar .avatar-circle {
    box-shadow: 0 4px 12px rgba(107, 139, 195, 0.4);
  }

  .language-chip {
    border-color: var(--border-color);
  }

  .card-action {
    background: var(--primary-color);
    box-shadow: 0 2px 8px rgba(107, 139, 195, 0.3);

    &:hover {
      background: var(--primary-color-light);
      box-shadow: 0 4px 12px rgba(107, 139, 195, 0.4);
    }
  }
}

/* ===== RESPONSIVE DESIGN ===== */

/* Tablets y pantallas medianas */
@media (max-width: 768px) {
  .story-card {
    border-radius: 16px;

    &:hover {
      transform: translateY(-2px);
    }
  }

  .card-avatar .avatar-circle {
    width: 44px;
    height: 44px;
    font-size: 1.1rem;
  }

  .card-title {
    font-size: 1rem;
  }

  .card-content {
    padding: 14px 16px 16px;
    gap: 14px;
  }

  .detail-item {
    font-size: 0.85rem;

    ion-icon {
      font-size: 0.95rem;
    }
  }

  .card-action {
    font-size: 0.85rem;

    ion-icon {
      font-size: 1rem;
    }
  }
}

/* Móviles */
@media (max-width: 480px) {
  .story-card {
    border-radius: 14px;

    &:hover {
      transform: translateY(-1px);
    }
  }

  .header-content {
    gap: 10px;
    margin-bottom: 14px;
  }

  .card-avatar .avatar-circle {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .card-title {
    font-size: 0.95rem;
    line-height: 1.3;
  }

  .button-delete {
    width: 32px;
    height: 32px;
    padding: 6px;

    ion-icon {
      font-size: 1.1rem;
    }
  }

  .card-meta {
    gap: 6px;
  }

  .language-chip {
    height: 26px;
    font-size: 0.75rem;

    ion-icon {
      font-size: 0.85rem;
    }
  }

  .difficulty-badge {
    font-size: 0.7rem;
    padding: 3px 8px;
  }

  .card-content {
    padding: 12px 14px 14px;
    gap: 12px;
  }

  .detail-item {
    font-size: 0.8rem;
    gap: 6px;

    ion-icon {
      font-size: 0.9rem;
    }
  }

  .card-action {
    font-size: 0.8rem;
    padding-top: 10px;

    ion-icon {
      font-size: 0.95rem;
    }
  }
}
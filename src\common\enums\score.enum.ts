// Enums que coinciden con el backend
export enum ScoreActionType {
  CREATE_STORY = "CREATE_STORY",
  COMPLETE_STORY = "COMPLETE_STORY",
  DAILY_LOGIN = "DAILY_LOGIN",
}

// Configuración de puntos por acción (coincide con backend)
export const SCORE_POINTS: Record<ScoreActionType, number> = {
  [ScoreActionType.CREATE_STORY]: 10,
  [ScoreActionType.COMPLETE_STORY]: 25,
  [ScoreActionType.DAILY_LOGIN]: 5,
};

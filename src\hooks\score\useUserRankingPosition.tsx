import { useMemo } from 'react';
import { useUser } from '../useUser';
import { useScoreRanking } from './useScoreRanking';

export const useUserRankingPosition = () => {
  const { user } = useUser();
  const { ranking, isLoading } = useScoreRanking();

  // Memoizar la posición del usuario para evitar recálculos innecesarios
  const userPosition = useMemo(() => {
    if (!user?.id || !ranking.length) return null;
    
    // Buscar la posición del usuario en el ranking
    const userRanking = ranking.find(entry => entry.userId === user.id);
    return userRanking?.rank || null;
  }, [user?.id, ranking]);

  // Función para obtener el color según la posición
  const getRankingColor = useMemo(() => {
    if (!userPosition) return 'var(--ion-color-warning)'; // Color por defecto
    
    if (userPosition <= 10) {
      return '#00CED1'; // Turquesa - Platino
    } else if (userPosition <= 20) {
      return '#FFD700'; // Oro
    } else if (userPosition <= 30) {
      return '#C0C0C0'; // Plata
    } else {
      return 'var(--ion-color-warning)'; // Color por defecto
    }
  }, [userPosition]);

  return {
    userPosition,
    rankingColor: getRankingColor,
    isLoading,
  };
};

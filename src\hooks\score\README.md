# Score System Hooks

Este directorio contiene todos los hooks relacionados con el sistema de puntuación de la aplicación.

## Estructura de Archivos

```
src/hooks/score/
├── useScore.tsx              # Hook principal con Context Provider
├── useUserScore.tsx          # Hook para obtener puntos del usuario actual
├── useScoreActions.tsx       # Hook para acciones que otorgan puntos
├── useScoreRanking.tsx       # Hook para obtener rankings de usuarios
├── useUserScoreCache.tsx     # Sistema de cache global para user scores
├── index.ts                  # Exportaciones centralizadas
├── README.md                 # Esta documentación
└── CACHE_SYSTEM.md           # 📚 Documentación detallada del sistema de cache
```

## Hooks Disponibles

### `useScore()`
Hook principal que proporciona acceso completo al sistema de puntuación.
- **Funciones**: `addPoints`, `getUserScore`, `getRanking`, etc.
- **Estado**: `isLoading`, `error`
- **Uso**: Para operaciones avanzadas del sistema de puntos

### `useUserScore(userId?: string)`
Hook especializado para obtener los puntos del usuario actual.
- **Auto-fetch**: Carga automáticamente los puntos cuando el usuario está disponible
- **Prevención de bucles**: Evita llamadas repetitivas al API
- **Retorna**: `totalScore`, `userScore`, `isLoading`, `error`

### `useScoreActions()`
Hook para acciones rápidas que otorgan puntos al usuario actual.
- **Funciones**: `addStoryCreationPoints`, `addStoryCompletionPoints`, `addDailyLoginPoints`
- **Auto-usuario**: Usa automáticamente el ID del usuario logueado
- **Retorna**: Funciones de acción + `isLoading`, `error`

### `useScoreRanking()`
Hook simplificado para obtener rankings de usuarios con SWR.
- **Estado**: `ranking`, `isLoading`, `error`
- **Características**:
  - Cache automático con SWR (30 minutos)
  - Límite fijo de 30 usuarios
  - Manejo automático de errores
  - Configuración optimizada para rankings

## Uso Recomendado

### Para mostrar puntos del usuario
```typescript
import { useUserScore } from './hooks/score';

const { totalScore, isLoading } = useUserScore();
```

### Para otorgar puntos por acciones
```typescript
import { useScoreActions } from './hooks/score';

const { addStoryCreationPoints } = useScoreActions();
await addStoryCreationPoints('Mi Cuento');
```

### Para mostrar rankings
```typescript
import { useScoreRanking } from './hooks/score';

const { ranking, isLoading, error, refetch } = useScoreRanking();

// Los datos se cargan automáticamente
// Para refrescar manualmente:
const handleRefresh = async () => {
  await refetch();
};
```

### Importación centralizada
```typescript
// Importar todo desde el índice
import {
  useScore,
  useUserScore,
  useScoreActions,
  useScoreRanking,
  ScoreProvider
} from './hooks/score';
```

## Provider Requerido

Todos los hooks requieren que la aplicación esté envuelta en `ScoreProvider` y `UserScoreCacheProvider`:

```typescript
import { ScoreProvider, UserScoreCacheProvider } from './hooks/score';

function App() {
  return (
    <ScoreProvider>
      <UserScoreCacheProvider>
        {/* Tu aplicación */}
      </UserScoreCacheProvider>
    </ScoreProvider>
  );
}
```

## 🔧 Sistema de Cache

El sistema incluye un **cache global avanzado** que elimina llamadas redundantes al API y mejora significativamente la performance:

- ✅ **Cache global** compartido entre todos los componentes
- ✅ **Eliminación de llamadas duplicadas** al API
- ✅ **Navegación instantánea** entre tabs
- ✅ **Invalidación selectiva** para datos actualizados
- ✅ **66% reducción** en llamadas al API
- ✅ **>99% mejora** en tiempos de respuesta

### 📚 Documentación Detallada
Para información completa sobre el sistema de cache, consulta:
**[📖 CACHE_SYSTEM.md](./CACHE_SYSTEM.md)**

La documentación incluye:
- Arquitectura completa del sistema
- Flujos de funcionamiento detallados
- API completa del cache
- Ejemplos prácticos de uso
- Patrones avanzados y optimizaciones
- Guías de testing y debugging
- Troubleshooting y mejores prácticas

## 🚀 Próximos Pasos

1. **Implementar backend**: Conectar con API real
2. **Agregar más acciones**: Nuevas formas de ganar puntos
3. **Sistema de logros**: Badges y achievements
4. **Notificaciones**: Alertas de subida de nivel
5. **Persistencia**: Guardar datos en base de datos
6. **Cache TTL**: Implementar expiración automática de cache
7. **Cache persistente**: Guardar cache en localStorage


import {
  <PERSON>H<PERSON>er,
  IonToolbar,
  IonText,
  IonIcon,
  IonBadge,
  IonButton,
} from "@ionic/react";
import { useUser } from "../../hooks/useUser";
import { star, mailOutline, moon, sunny } from "ionicons/icons";
import { useTheme } from "../../hooks/useTheme";
import { useUserScore, useUserRankingPosition } from "../../hooks/score";
import "./Header.scss";

export default function Header() {
  const { user } = useUser();
  const { theme, toggleTheme } = useTheme();
  const { totalScore } = useUserScore();
  const { rankingColor } = useUserRankingPosition();
  const userName = user?.name || "Invitado";
  // Simulación de notificaciones no leídas
  const unreadNotifications = 3; // Cambia esto según tu lógica

  return (
    <IonHeader>
      <IonToolbar style={{ padding: 0 }}>
        <div className="header-grid-fix">
          <div className="header-col user-name">
            <IonText className="user-name-text">
              {userName}
            </IonText>
          </div>
          <div className="header-col points-center">
            <IonIcon
              icon={star}
              className="points-icon"
              style={{ color: rankingColor }}
            />
            <span className="points-value">{totalScore}</span>
          </div>
          <div className="header-col notification-icon-outer">
            <div className="theme-container">
              <IonButton
                fill="clear"
                onClick={toggleTheme}
                className="theme-toggle-btn"
                title="Cambiar tema"
              >
                <IonIcon
                  icon={theme === "dark" ? sunny : moon}
                  slot="icon-only"
                />
              </IonButton>
            </div>
            <div className="notification-icon-container-fix">
              <IonIcon icon={mailOutline} className="notification-icon" />
              {unreadNotifications > 0 && (
                <IonBadge color="danger" className="notification-badge-fix">
                  {unreadNotifications}
                </IonBadge>
              )}
            </div>
          </div>
        </div>
      </IonToolbar>
    </IonHeader>
  );
}

1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.storytale.creator"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <!-- Permissions -->
12
13    <uses-permission android:name="android.permission.INTERNET" />
13-->C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\AndroidManifest.xml:40:5-67
13-->C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\AndroidManifest.xml:40:22-64
14
15    <queries>
15-->[:capacitor-community-text-to-speech] C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\node_modules\@capacitor-community\text-to-speech\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-11:15
16        <intent>
16-->[:capacitor-community-text-to-speech] C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\node_modules\@capacitor-community\text-to-speech\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:18
17            <action android:name="android.intent.action.TTS_SERVICE" />
17-->[:capacitor-community-text-to-speech] C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\node_modules\@capacitor-community\text-to-speech\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-72
17-->[:capacitor-community-text-to-speech] C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\node_modules\@capacitor-community\text-to-speech\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-69
18        </intent>
19    </queries>
20
21    <uses-permission android:name="android.permission.VIBRATE" />
21-->[:capacitor-haptics] C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
21-->[:capacitor-haptics] C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-63
22
23    <permission
23-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
24        android:name="com.storytale.creator.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
24-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
25        android:protectionLevel="signature" />
25-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
26
27    <uses-permission android:name="com.storytale.creator.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
27-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
27-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
28
29    <application
29-->C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\AndroidManifest.xml:4:5-36:19
30        android:allowBackup="true"
30-->C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\AndroidManifest.xml:5:9-35
31        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
31-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
32        android:debuggable="true"
33        android:extractNativeLibs="false"
34        android:icon="@mipmap/ic_launcher"
34-->C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\AndroidManifest.xml:6:9-43
35        android:label="@string/app_name"
35-->C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\AndroidManifest.xml:7:9-41
36        android:roundIcon="@mipmap/ic_launcher_round"
36-->C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\AndroidManifest.xml:8:9-54
37        android:supportsRtl="true"
37-->C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\AndroidManifest.xml:9:9-35
38        android:theme="@style/AppTheme" >
38-->C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\AndroidManifest.xml:10:9-40
39        <activity
39-->C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\AndroidManifest.xml:12:9-25:20
40            android:name="com.storytale.creator.MainActivity"
40-->C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\AndroidManifest.xml:14:13-41
41            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|smallestScreenSize|screenLayout|uiMode|navigation"
41-->C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\AndroidManifest.xml:13:13-140
42            android:exported="true"
42-->C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\AndroidManifest.xml:18:13-36
43            android:label="@string/title_activity_main"
43-->C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\AndroidManifest.xml:15:13-56
44            android:launchMode="singleTask"
44-->C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\AndroidManifest.xml:17:13-44
45            android:theme="@style/AppTheme.NoActionBarLaunch" >
45-->C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\AndroidManifest.xml:16:13-62
46            <intent-filter>
46-->C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\AndroidManifest.xml:20:13-23:29
47                <action android:name="android.intent.action.MAIN" />
47-->C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\AndroidManifest.xml:21:17-69
47-->C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\AndroidManifest.xml:21:25-66
48
49                <category android:name="android.intent.category.LAUNCHER" />
49-->C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\AndroidManifest.xml:22:17-77
49-->C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\AndroidManifest.xml:22:27-74
50            </intent-filter>
51        </activity>
52
53        <provider
54            android:name="androidx.core.content.FileProvider"
54-->C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\AndroidManifest.xml:28:13-62
55            android:authorities="com.storytale.creator.fileprovider"
55-->C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\AndroidManifest.xml:29:13-64
56            android:exported="false"
56-->C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\AndroidManifest.xml:30:13-37
57            android:grantUriPermissions="true" >
57-->C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\AndroidManifest.xml:31:13-47
58            <meta-data
58-->C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\AndroidManifest.xml:32:13-34:64
59                android:name="android.support.FILE_PROVIDER_PATHS"
59-->C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\AndroidManifest.xml:33:17-67
60                android:resource="@xml/file_paths" />
60-->C:\Users\<USER>\OneDrive\Desktop\apps\cuentos\story_application_frontend\android\app\src\main\AndroidManifest.xml:34:17-51
61        </provider>
62        <provider
62-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
63            android:name="androidx.startup.InitializationProvider"
63-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
64            android:authorities="com.storytale.creator.androidx-startup"
64-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
65            android:exported="false" >
65-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
66            <meta-data
66-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
67                android:name="androidx.emoji2.text.EmojiCompatInitializer"
67-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
68                android:value="androidx.startup" />
68-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
69            <meta-data
69-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
70                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
70-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
71                android:value="androidx.startup" />
71-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
72            <meta-data
72-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
73                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
73-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
74                android:value="androidx.startup" />
74-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
75        </provider>
76
77        <receiver
77-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
78            android:name="androidx.profileinstaller.ProfileInstallReceiver"
78-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
79            android:directBootAware="false"
79-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
80            android:enabled="true"
80-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
81            android:exported="true"
81-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
82            android:permission="android.permission.DUMP" >
82-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
83            <intent-filter>
83-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
84                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
84-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
84-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
85            </intent-filter>
86            <intent-filter>
86-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
87                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
87-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
87-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
88            </intent-filter>
89            <intent-filter>
89-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
90                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
90-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
90-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
91            </intent-filter>
92            <intent-filter>
92-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
93                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
93-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
93-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
94            </intent-filter>
95        </receiver>
96    </application>
97
98</manifest>
